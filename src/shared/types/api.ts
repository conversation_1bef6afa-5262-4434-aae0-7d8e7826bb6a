import type { ApiRequestBody, ApiResponseBody, Schema } from "./common";

// Common
export type UserSession = GetMeResponse["data"];
export type BaseErrorResponse = Schema<"BaseErrorResponseDTO">;
export type BaseResponse = Schema<"BaseResponseDTO">;
export type BasePaginationResponse = Schema<"BasePaginationResponseDTO">;
export type BulkActionPayload = Schema<"BulkActionDTO">;
export type BulkActionResponse = Schema<"BaseResponseDTO">;

// Auth
export type GetMeResponse = ApiResponseBody<"/api/v1/auth/me", "get", 200>;
export type LoginPayload = ApiRequestBody<"/api/v1/admin/auth/login", "post">;
export type LoginResponse = ApiResponseBody<
	"/api/v1/admin/auth/login",
	"post",
	200
>;
export type LogoutResponse = ApiResponseBody<
	"/api/v1/auth/logout",
	"post",
	200
>;
export type ForgotPasswordPayload = ApiRequestBody<
	"/api/v1/admin/auth/forgot-password",
	"post"
>;
export type ForgotPasswordResponse = ApiResponseBody<
	"/api/v1/admin/auth/forgot-password",
	"post",
	200
>;
export type ResetPasswordPayload = ApiRequestBody<
	"/api/v1/admin/auth/reset-password",
	"post"
>;
export type ResetPasswordResponse = ApiResponseBody<
	"/api/v1/admin/auth/reset-password",
	"post",
	200
>;

// User
export type User = Schema<"User">;
export type GetAllUserResponse = ApiResponseBody<
	"/api/v1/admin/users",
	"get",
	200
>;
export type GetUserResponse = ApiResponseBody<
	"/api/v1/admin/users/{userId}",
	"get",
	200
>;
export type GetUserTreeResponse = ApiResponseBody<
	"/api/v1/admin/users/tree",
	"get",
	200
>;
export type GetUserOptionsResponse = ApiResponseBody<
	"/api/v1/admin/users/options",
	"get",
	200
>;
export type CreateUserPayload = ApiRequestBody<"/api/v1/admin/users", "post">;
export type CreateUserResponse = ApiResponseBody<
	"/api/v1/admin/users",
	"post",
	201
>;
export type UpdateUserPayload = ApiRequestBody<
	"/api/v1/admin/users/{userId}",
	"put"
>;
export type UpdateUserResponse = ApiResponseBody<
	"/api/v1/admin/users/{userId}",
	"put",
	200
>;

// Administrator User
export type AdministratorUser = Schema<"AdministratorUser">;
export type GetAllAdministratorUserResponse = ApiResponseBody<
	"/api/v1/admin/administrator-users",
	"get",
	200
>;
export type GetAdministratorUserResponse = ApiResponseBody<
	"/api/v1/admin/administrator-users/{administratorUserId}",
	"get",
	200
>;
export type CreateAdministratorUserPayload = ApiRequestBody<
	"/api/v1/admin/administrator-users",
	"post"
>;
export type CreateAdministratorUserResponse = ApiResponseBody<
	"/api/v1/admin/administrator-users",
	"post",
	201
>;
export type UpdateAdministratorUserPayload = ApiRequestBody<
	"/api/v1/admin/administrator-users/{administratorUserId}",
	"put"
>;
export type UpdateAdministratorUserResponse = ApiResponseBody<
	"/api/v1/admin/administrator-users/{administratorUserId}",
	"put",
	200
>;

// Role
export type Role = Schema<"Role">;
export type GetAllRoleResponse = ApiResponseBody<
	"/api/v1/admin/roles",
	"get",
	200
>;
export type GetRoleResponse = ApiResponseBody<
	"/api/v1/admin/roles/{roleId}",
	"get",
	200
>;
export type GetRoleOptionsResponse = ApiResponseBody<
	"/api/v1/admin/roles/options",
	"get",
	200
>;
export type GetSupervisorOptionsResponse = ApiResponseBody<
	"/api/v1/admin/roles/{roleId}/supervisors/options",
	"get",
	200
>;
export type GetRoleTreeResponse = Schema<"GetTreeNodeResponseDTO">;
export type CreateRolePayload = ApiRequestBody<"/api/v1/admin/roles", "post">;
export type CreateRoleResponse = ApiResponseBody<
	"/api/v1/admin/roles",
	"post",
	201
>;
export type UpdateRolePayload = ApiRequestBody<
	"/api/v1/admin/roles/{roleId}",
	"put"
>;
export type UpdateRoleResponse = ApiResponseBody<
	"/api/v1/admin/roles/{roleId}",
	"put",
	200
>;

// Worksite
export type Worksite = Schema<"Worksite">;
export type GetWorksiteOptionsResponse = ApiResponseBody<
	"/api/v1/admin/worksites/options",
	"get",
	200
>;

// Attendance
export type AttendanceLog = Schema<"AttendanceLog">;
export type GetAllAttendanceLogResponse = ApiResponseBody<
	"/api/v1/admin/attendance",
	"get",
	200
>;
export type GetAttendanceLogResponse = ApiResponseBody<
	"/api/v1/admin/attendance/{attendanceLogId}",
	"get",
	200
>;
export type CreateAttendanceLogPayload = FormData;
export type CreateAttendanceLogResponse = ApiResponseBody<
	"/api/v1/admin/attendance",
	"post",
	201
>;
export type UpdateAttendanceLogPayload = FormData;
export type UpdateAttendanceLogResponse = ApiResponseBody<
	"/api/v1/admin/attendance/{attendanceLogId}",
	"put",
	200
>;

// Attendance Rule
export type AttendanceRule = Schema<"AttendanceRule">;
export type GetAllAttendanceRuleResponse = ApiResponseBody<
	"/api/v1/admin/attendance-rules",
	"get",
	200
>;
export type GetAttendanceRuleByWorksiteResponse = ApiResponseBody<
	"/api/v1/admin/worksites/{worksiteId}/attendance-rule",
	"get",
	200
>;
export type UpdateAttendanceRulePayload = ApiRequestBody<
	"/api/v1/admin/attendance-rules/{attendanceRuleId}",
	"put"
>;
export type UpdateAttendanceRuleResponse = ApiResponseBody<
	"/api/v1/admin/attendance-rules/{attendanceRuleId}",
	"put",
	200
>;

// OfficeLeave
export type OfficeLeave = Schema<"OfficeLeave">;
export type OfficeLeaveResponse = Schema<"OfficeLeaveResponseDTO">;
export type GetAllOfficeLeaveResponse = ApiResponseBody<
	"/api/v1/admin/office-leaves",
	"get",
	200
>;
export type GetOfficeLeaveResponse = ApiResponseBody<
	"/api/v1/admin/office-leaves/{officeLeaveId}",
	"get",
	200
>;
export type CreateOfficeLeavePayload = ApiRequestBody<
	"/api/v1/admin/office-leaves",
	"post"
>;
export type CreateOfficeLeaveResponse = ApiResponseBody<
	"/api/v1/admin/office-leaves",
	"post",
	201
>;
export type UpdateOfficeLeavePayload = ApiRequestBody<
	"/api/v1/admin/office-leaves/{officeLeaveId}",
	"put"
>;
export type UpdateOfficeLeaveResponse = ApiResponseBody<
	"/api/v1/admin/office-leaves/{officeLeaveId}",
	"put",
	200
>;

// Leave Policy
export type LeavePolicy = Schema<"LeavePolicy">;
export type LeavePolicyResponse = Schema<"LeavePolicyResponseDTO">;
export type GetAllLeavePolicyResponse = ApiResponseBody<
	"/api/v1/admin/leave-policies",
	"get",
	200
>;
export type GetLeavePolicyResponse = ApiResponseBody<
	"/api/v1/admin/leave-policies/{leavePolicyId}",
	"get",
	200
>;
export type GetLeavePolicyOptionsResponse = ApiResponseBody<
	"/api/v1/admin/leave-policies/options",
	"get",
	200
>;
export type CreateLeavePolicyPayload = ApiRequestBody<
	"/api/v1/admin/leave-policies",
	"post"
>;
export type CreateLeavePolicyResponse = ApiResponseBody<
	"/api/v1/admin/leave-policies",
	"post",
	201
>;
export type UpdateLeavePolicyPayload = ApiRequestBody<
	"/api/v1/admin/leave-policies/{leavePolicyId}",
	"put"
>;
export type UpdateLeavePolicyResponse = ApiResponseBody<
	"/api/v1/admin/leave-policies/{leavePolicyId}",
	"put",
	200
>;

// Leave Request
export type LeaveRequest = Schema<"LeaveRequest">;
export type LeaveRequestResponse = Schema<"LeaveRequestResponseDTO">;
export type GetAllLeaveRequestResponse = ApiResponseBody<
	"/api/v1/admin/leave-requests",
	"get",
	200
>;
export type GetLeaveRequestResponse = ApiResponseBody<
	"/api/v1/admin/leave-requests/{leaveRequestId}",
	"get",
	200
>;
export type CreateLeaveRequestPayload = FormData;
export type CreateLeaveRequestResponse = ApiResponseBody<
	"/api/v1/admin/leave-requests",
	"post",
	201
>;
export type UpdateLeaveRequestPayload = FormData;
export type UpdateLeaveRequestResponse = ApiResponseBody<
	"/api/v1/admin/leave-requests/{leaveRequestId}",
	"put",
	200
>;

// Task
export type Task = Schema<"Task">;
export type TaskResponse = Schema<"TaskResponseDTO">;
export type GetAllTaskResponse = ApiResponseBody<
	"/api/v1/admin/tasks",
	"get",
	200
>;
export type GetTaskResponse = ApiResponseBody<
	"/api/v1/admin/tasks/{taskId}",
	"get",
	200
>;
export type GetTaskAssignableUsersResponse = ApiResponseBody<
	"/api/v1/admin/tasks/assignable-users",
	"get",
	200
>;
export type GetTaskAssignerUsersResponse = ApiResponseBody<
	"/api/v1/admin/tasks/assigner-users",
	"get",
	200
>;
export type CreateTaskPayload = FormData;
export type CreateTaskResponse = ApiResponseBody<
	"/api/v1/admin/tasks",
	"post",
	201
>;
export type UpdateTaskPayload = FormData;
export type UpdateTaskResponse = ApiResponseBody<
	"/api/v1/admin/tasks/{taskId}",
	"put",
	200
>;
